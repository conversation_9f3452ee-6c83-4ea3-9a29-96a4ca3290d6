# ============================================================================
# PPO停车环境测试配置文件
# ============================================================================

# 基础框架配置
dl_toolbox: "torch"  # 深度学习框架选择：torch, mindspore, tensorlayer
project_name: "XuanCe_Test"  # 项目名称
logger: "tensorboard"  # 日志记录器类型：tensorboard或wandb
wandb_user_name: "your_user_name"  # wandb用户名（当使用wandb时）

# 渲染和测试配置
render: True  # 测试时启用渲染以观察智能体行为
render_mode: 'human'  # 渲染模式：human（实时显示）或rgb_array（返回图像数组）
fps: 30  # 渲染视频的帧率
test_mode: True  # 运行在测试模式

# 计算设备配置
device: "cuda:0"  # 计算设备选择：cuda:0表示第一块GPU，cpu表示使用CPU
distributed_training: False  # 是否使用多GPU分布式训练
master_port: '12355'  # 分布式训练时的主端口

# 智能体和环境配置
agent: "PPO_Clip"  # 智能体名称
env_name: "custom_parking"  # 环境名称 - 使用自定义停车环境
env_id: "parking-v0"  # 环境ID
env_seed: 42  # 环境随机种子
vectorize: "DummyVecEnv"  # 并行环境创建方法：DummyVecEnv（单进程）或SubprocVecEnv（多进程）
learner: "PPOCLIP_Learner"  # 学习器类型
policy: "Gaussian_AC"  # 策略类型：连续动作用Gaussian_AC，离散动作用Categorical_AC
representation: "Basic_MLP"  # 表示网络名称

# 网络结构配置（与训练时保持一致）
representation_hidden_size: [ 256, ]  # 表示网络隐藏层大小
actor_hidden_size: [ 256, ]  # 演员网络隐藏层大小
critic_hidden_size: [ 256, ]  # 评论家网络隐藏层大小
activation: "leaky_relu"  # 隐藏层激活函数
activation_action: 'tanh'  # 演员网络最后一层的激活函数

# 测试基础参数
seed: null  # 随机种子设为null以获得真正的随机性
parallels: 1  # 测试时使用单个环境
running_steps: 1000  # 测试的总运行步数
horizon_size: 128  # 环境的时间步长
max_episode_steps: 150  # 每个回合的最大时间步数
n_epochs: 1  # 测试时不需要多轮训练
n_minibatch: 1  # 测试时不需要小批次
learning_rate: 0.0003  # 学习率（测试时不使用，但需要保持一致）
use_linear_lr_decay: False  # 测试时不使用学习率衰减
end_factor_lr_decay: 0.1  # 学习率衰减的最终因子

# PPO算法参数（与训练时保持一致）
vf_coef: 0.25  # 价值函数损失系数
ent_coef: 0.01  # 熵损失系数
target_kl: 0.25  # PPO_KL学习器的目标KL散度
kl_coef: 1.0  # PPO_KL学习器的KL系数
clip_range: 0.2  # PPO_Clip学习器中比率的裁剪范围
gamma: 0.99  # 折扣因子
use_gae: True  # 使用GAE（广义优势估计）技巧
gae_lambda: 0.95  # GAE的lambda参数
use_advnorm: True  # 是否使用优势标准化

# 训练优化配置（测试时不使用，但需要保持一致）
use_grad_clip: True  # 是否在训练时裁剪梯度
clip_type: 1  # Mindspore的梯度裁剪类型：0表示按值裁剪，1表示按范数裁剪
grad_clip_norm: 0.5  # 梯度的最大范数
use_actions_mask: False  # 是否使用动作掩码值
use_obsnorm: True  # 是否使用观测标准化
use_rewnorm: True  # 是否使用奖励标准化
obsnorm_range: 5  # 观测标准化的范围
rewnorm_range: 5  # 奖励标准化的范围

# 测试专用配置
test_steps: 10000  # 测试的总步数
eval_interval: 1000  # 评估间隔（测试时不使用）
test_episode: 10  # 测试回合数 - 增加测试回合数以获得更稳定的结果
log_dir: "./result/test/logs/"  # 测试日志文件的主目录
model_dir: "./result/test/models/"  # 测试模型文件的主目录

# 模型加载配置
model_dir_load: "./result/train/models/seed_79811_2025_0731_124745/"  # 加载训练好的模型的路径
model_name_load: "best_model.pth"  # 要加载的模型文件名

# 测试结果保存配置
save_test_results: True  # 是否保存测试结果
test_results_dir: "./result/test/results/"  # 测试结果保存目录

# 随机生成配置
enable_random_spawn: True  # 是否启用随机初始位置生成
spawn_margin: 8.0  # 与边界的安全距离（米）