# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional, List, Tuple, Dict, Any, Union

# 类型别名
State = Tuple[float, float, float]  # (x, y, theta)
Path = List[State]
Motion = Tuple[float, float]  # (distance, steering_angle)
import heapq
import sys
import os

# 添加HybridAstarPlanner路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../HybridAstarPlanner'))


# 移除专业版本导入，使用简化版本


class SimpleHybridAStar:
    """极简但有效的Hybrid A*路径规划器"""

    def __init__(self):
        # 简化参数
        self.step_size = 2.0
        self.wheelbase = 2.5

        # 简化的运动原语
        self.motions = [
            (2.0, 0),  # 直行
            (2.0, 0.3),  # 左转
            (2.0, -0.3),  # 右转
            (-1.5, 0),  # 后退
            (-1.5, 0.3),  # 左转后退
            (-1.5, -0.3),  # 右转后退
        ]

        # 停车场布局信息
        self.parking_layout = None
        self.obstacle_map = None
        self.grid_resolution = 1.0  # 网格分辨率（米）

    def plan(self, start_state: State, goal_state: State, obstacles: List[dict]) -> Optional[Path]:
        """主路径规划接口：基于停车场布局生成可行驾驶轨迹"""

        # 首先构建停车场布局地图
        self._build_parking_layout_map(obstacles)

        # 策略1：尝试基于布局的A*搜索
        layout_path = self._layout_based_astar(start_state, goal_state)
        if layout_path and len(layout_path) > 2:
            return layout_path

        # 策略2：回退到基于车辆运动学的智能路径生成
        reasonable_path = self._generate_smart_path(start_state, goal_state, obstacles)
        if reasonable_path and len(reasonable_path) > 3:  # 检查路径质量
            return reasonable_path

        # 策略2：使用Hybrid A*搜索算法
        path = self._hybrid_astar_search(start_state, goal_state, obstacles)
        if path and len(path) > 3:  # 检查搜索结果质量
            return path

        # 策略3：生成简单直线路径作为备选
        return self._generate_simple_path(start_state, goal_state)

    def _simple_astar(self, start_state, goal_state, obstacles):
        """真正的Hybrid A*搜索 - 考虑车辆运动学"""
        open_list = []
        closed_set = set()
        came_from = {}
        g_costs = {}

        start_node = self._state_to_node(start_state)
        g_costs[start_node] = 0
        h_cost = self._heuristic(start_state, goal_state)
        heapq.heappush(open_list, (h_cost, start_node, start_state))

        for iteration in range(150):  # 适中的迭代次数
            if not open_list:
                break

            _, current_node, current_state = heapq.heappop(open_list)

            if current_node in closed_set:
                continue
            closed_set.add(current_node)

            # 目标检查
            if self._is_goal_reached(current_state, goal_state):
                path = self._reconstruct_path(came_from, current_node, start_node)
                path.append(current_state)  # 添加目标状态
                return path

            # 扩展邻居 - 使用车辆运动学
            for motion in self.motions:
                new_state = self._apply_motion_with_kinematics(current_state, motion)
                new_node = self._state_to_node(new_state)

                if new_node not in closed_set and self._is_collision_free(new_state, obstacles):
                    # 计算真实代价
                    motion_cost = self._motion_cost(motion)
                    tentative_g_cost = g_costs[current_node] + motion_cost

                    if new_node not in g_costs or tentative_g_cost < g_costs[new_node]:
                        g_costs[new_node] = tentative_g_cost
                        h_cost = self._heuristic(new_state, goal_state)
                        f_cost_new = tentative_g_cost + h_cost

                        came_from[new_node] = (current_node, new_state)
                        heapq.heappush(open_list, (f_cost_new, new_node, new_state))

        return None

    def _generate_simple_path(self, start_state, goal_state):
        """生成简单路径"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 添加一个中间点
        mid_x = (x1 + x2) / 2
        mid_y = (y1 + y2) / 2
        mid_theta = (theta1 + theta2) / 2

        return [start_state, (mid_x, mid_y, mid_theta), goal_state]

    def _build_parking_layout_map(self, obstacles: List[dict]):
        """构建停车场布局地图，标识可行驶区域和障碍物"""
        # 停车场边界 (基于父类设置)
        self.map_width = 70  # 停车场宽度
        self.map_height = 42  # 停车场高度
        self.map_center_x = 0
        self.map_center_y = 0

        # 创建网格地图 (1米分辨率)
        grid_width = int(self.map_width / self.grid_resolution)
        grid_height = int(self.map_height / self.grid_resolution)

        # 初始化地图：0=可行驶，1=障碍物，2=停车位
        self.obstacle_map = np.zeros((grid_height, grid_width), dtype=int)

        # 1. 标记停车位区域
        self._mark_parking_spots()

        # 2. 标记障碍物（其他车辆）
        self._mark_obstacles(obstacles)

        # 3. 标记边界墙壁
        self._mark_walls()

        # 4. 定义可行驶区域（中央通道）
        self._mark_drivable_areas()

    def _mark_parking_spots(self):
        """标记停车位区域为不可穿越"""
        # 上排停车位: y=+10到+18
        # 下排停车位: y=-10到-18
        # 每个停车位宽度4米，长度8米

        spots = 14
        spot_width = 4.0
        spot_length = 8.0
        x_offset = 4.0  # 停车位间距

        for k in range(spots):
            # 计算停车位的x坐标
            x = (k + 1 - spots // 2) * (spot_width + x_offset) - spot_width / 2

            # 转换为网格坐标
            grid_x_start = int((x + self.map_width/2) / self.grid_resolution)
            grid_x_end = int((x + spot_width + self.map_width/2) / self.grid_resolution)

            # 上排停车位
            grid_y_start_upper = int((10 + self.map_height/2) / self.grid_resolution)
            grid_y_end_upper = int((18 + self.map_height/2) / self.grid_resolution)

            # 下排停车位
            grid_y_start_lower = int((-18 + self.map_height/2) / self.grid_resolution)
            grid_y_end_lower = int((-10 + self.map_height/2) / self.grid_resolution)

            # 标记停车位区域
            if 0 <= grid_x_start < self.obstacle_map.shape[1] and 0 <= grid_x_end < self.obstacle_map.shape[1]:
                # 上排
                if 0 <= grid_y_start_upper < self.obstacle_map.shape[0] and 0 <= grid_y_end_upper < self.obstacle_map.shape[0]:
                    self.obstacle_map[grid_y_start_upper:grid_y_end_upper, grid_x_start:grid_x_end] = 2
                # 下排
                if 0 <= grid_y_start_lower < self.obstacle_map.shape[0] and 0 <= grid_y_end_lower < self.obstacle_map.shape[0]:
                    self.obstacle_map[grid_y_start_lower:grid_y_end_lower, grid_x_start:grid_x_end] = 2

    def _mark_obstacles(self, obstacles: List[dict]):
        """标记障碍物（其他车辆）位置"""
        for obs in obstacles:
            if 'position' in obs:
                x, y = obs['position']
                # 车辆尺寸（长5米，宽2米）
                vehicle_length = 5.0
                vehicle_width = 2.0

                # 转换为网格坐标
                grid_x = int((x + self.map_width/2) / self.grid_resolution)
                grid_y = int((y + self.map_height/2) / self.grid_resolution)

                # 标记车辆占据的区域
                half_length = int(vehicle_length / (2 * self.grid_resolution))
                half_width = int(vehicle_width / (2 * self.grid_resolution))

                for dy in range(-half_width, half_width + 1):
                    for dx in range(-half_length, half_length + 1):
                        gx, gy = grid_x + dx, grid_y + dy
                        if 0 <= gx < self.obstacle_map.shape[1] and 0 <= gy < self.obstacle_map.shape[0]:
                            self.obstacle_map[gy, gx] = 1

    def _mark_walls(self):
        """标记边界墙壁"""
        # 标记地图边界为障碍物
        self.obstacle_map[0, :] = 1  # 上边界
        self.obstacle_map[-1, :] = 1  # 下边界
        self.obstacle_map[:, 0] = 1  # 左边界
        self.obstacle_map[:, -1] = 1  # 右边界

    def _mark_drivable_areas(self):
        """标记可行驶区域（主要是中央通道）"""
        # 中央通道: y=-10到+10的区域
        grid_y_start = int((-10 + self.map_height/2) / self.grid_resolution)
        grid_y_end = int((10 + self.map_height/2) / self.grid_resolution)

        # 确保中央通道是可行驶的（值为0）
        for y in range(max(0, grid_y_start), min(self.obstacle_map.shape[0], grid_y_end)):
            for x in range(self.obstacle_map.shape[1]):
                if self.obstacle_map[y, x] == 2:  # 如果是停车位，改为可行驶（仅中央通道）
                    continue  # 保持停车位标记
                elif self.obstacle_map[y, x] == 0:  # 已经是可行驶区域
                    continue

    def _layout_based_astar(self, start_state: State, goal_state: State) -> Optional[Path]:
        """基于停车场布局的A*搜索"""
        if self.obstacle_map is None:
            return None

        # 转换起点和终点为网格坐标
        start_grid = self._world_to_grid(start_state[:2])
        goal_grid = self._world_to_grid(goal_state[:2])

        # 检查起点和终点是否在有效范围内
        if not self._is_valid_grid_position(start_grid) or not self._is_valid_grid_position(goal_grid):
            return None

        # A*搜索
        open_list = []
        closed_set = set()
        came_from = {}
        g_score = {start_grid: 0}

        # 启发式代价
        h_cost = self._grid_heuristic(start_grid, goal_grid)
        heapq.heappush(open_list, (h_cost, start_grid))

        while open_list:
            current_cost, current = heapq.heappop(open_list)

            if current in closed_set:
                continue

            closed_set.add(current)

            # 检查是否到达目标
            if self._grid_distance(current, goal_grid) < 2:  # 2格容差
                # 重构路径
                path = self._reconstruct_grid_path(came_from, current)
                # 转换回世界坐标并添加朝向信息
                return self._grid_path_to_world_path(path, start_state, goal_state)

            # 扩展邻居
            for neighbor in self._get_grid_neighbors(current):
                if neighbor in closed_set:
                    continue

                # 检查邻居是否可达（目标附近允许进入停车位）
                is_near_goal = self._grid_distance(neighbor, goal_grid) < 3
                if not self._is_valid_grid_position(neighbor, allow_parking_spots=is_near_goal):
                    continue

                # 计算新的g代价（包含路径偏好）
                base_cost = self._grid_distance(current, neighbor)
                path_preference_cost = self._get_path_preference_cost(neighbor)
                tentative_g = g_score[current] + base_cost + path_preference_cost

                if neighbor not in g_score or tentative_g < g_score[neighbor]:
                    came_from[neighbor] = current
                    g_score[neighbor] = tentative_g
                    f_score = tentative_g + self._grid_heuristic(neighbor, goal_grid)
                    heapq.heappush(open_list, (f_score, neighbor))

        return None  # 未找到路径

    def _world_to_grid(self, world_pos):
        """世界坐标转网格坐标"""
        x, y = world_pos
        grid_x = int((x + self.map_width/2) / self.grid_resolution)
        grid_y = int((y + self.map_height/2) / self.grid_resolution)
        return (grid_x, grid_y)

    def _grid_to_world(self, grid_pos):
        """网格坐标转世界坐标"""
        grid_x, grid_y = grid_pos
        x = grid_x * self.grid_resolution - self.map_width/2
        y = grid_y * self.grid_resolution - self.map_height/2
        return (x, y)

    def _is_valid_grid_position(self, grid_pos, allow_parking_spots=False):
        """检查网格位置是否有效（在边界内且不是障碍物）"""
        grid_x, grid_y = grid_pos
        if not (0 <= grid_x < self.obstacle_map.shape[1] and 0 <= grid_y < self.obstacle_map.shape[0]):
            return False

        cell_value = self.obstacle_map[grid_y, grid_x]

        if cell_value == 1:  # 障碍物（其他车辆）
            return False
        elif cell_value == 2:  # 停车位
            return allow_parking_spots  # 只有在允许时才能进入停车位
        else:  # cell_value == 0，可行驶区域
            return True

    def _get_grid_neighbors(self, grid_pos):
        """获取网格邻居（8方向）"""
        grid_x, grid_y = grid_pos
        neighbors = []
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                if dx == 0 and dy == 0:
                    continue
                neighbors.append((grid_x + dx, grid_y + dy))
        return neighbors

    def _grid_distance(self, pos1, pos2):
        """计算网格距离"""
        return np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

    def _grid_heuristic(self, pos1, pos2):
        """网格启发式函数"""
        return self._grid_distance(pos1, pos2)

    def _get_path_preference_cost(self, grid_pos):
        """计算路径偏好代价，鼓励使用中央通道"""
        grid_x, grid_y = grid_pos

        if not (0 <= grid_x < self.obstacle_map.shape[1] and 0 <= grid_y < self.obstacle_map.shape[0]):
            return 10.0  # 边界外高代价

        cell_value = self.obstacle_map[grid_y, grid_x]

        if cell_value == 0:  # 可行驶区域（中央通道）
            # 进一步鼓励在中央通道的中心区域行驶
            world_pos = self._grid_to_world(grid_pos)
            y_world = world_pos[1]

            # 中央通道是y=-10到+10，越靠近y=0代价越低
            if -10 <= y_world <= 10:
                center_distance = abs(y_world) / 10.0  # 0到1之间
                return 0.1 * center_distance  # 很小的代价，鼓励中央行驶
            else:
                return 0.5  # 在可行驶区域但不在中央通道

        elif cell_value == 2:  # 停车位区域
            return 2.0  # 较高代价，避免不必要地进入停车位

        else:  # cell_value == 1，障碍物
            return 100.0  # 极高代价，实际上不应该到达这里

    def _reconstruct_grid_path(self, came_from, current):
        """重构网格路径"""
        path = [current]
        while current in came_from:
            current = came_from[current]
            path.append(current)
        return path[::-1]

    def _grid_path_to_world_path(self, grid_path, start_state, goal_state):
        """将网格路径转换为世界坐标路径，并添加朝向信息"""
        if not grid_path:
            return None

        world_path = []
        for i, grid_pos in enumerate(grid_path):
            world_pos = self._grid_to_world(grid_pos)

            # 计算朝向
            if i == 0:
                # 起点使用原始朝向
                theta = start_state[2]
            elif i == len(grid_path) - 1:
                # 终点使用目标朝向
                theta = goal_state[2]
            else:
                # 中间点：朝向下一个点
                next_world_pos = self._grid_to_world(grid_path[i + 1])
                theta = np.arctan2(next_world_pos[1] - world_pos[1],
                                 next_world_pos[0] - world_pos[0])

            world_path.append((world_pos[0], world_pos[1], theta))

        return world_path

    def _generate_smart_path(self, start_state: State, goal_state: State, obstacles: List[dict]) -> Optional[Path]:
        """智能路径生成：根据车头朝向选择最适合的路径策略"""
        try:
            # 解析起点和终点状态
            x1, y1, theta1 = start_state  # 起点坐标和朝向
            x2, y2, theta2 = goal_state   # 终点坐标和朝向

            # 计算起点到终点的直线距离
            distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

            # 计算从起点到终点的方向角
            target_direction = np.arctan2(y2 - y1, x2 - x1)

            # 计算车头朝向与目标方向的角度差
            heading_diff = target_direction - theta1
            heading_diff = np.arctan2(np.sin(heading_diff), np.cos(heading_diff))  # 规范化到[-π,π]

            # 根据角度差选择最适合的路径生成策略
            if abs(heading_diff) < np.pi / 6:  # 角度差小于30度
                # 车头基本朝向目标，使用前进路径
                return self._generate_forward_path(start_state, goal_state)
            elif abs(heading_diff) > 2 * np.pi / 3:  # 角度差大于120度
                # 车头背离目标，需要复杂机动
                return self._generate_complex_path(start_state, goal_state)
            else:  # 角度差在30-120度之间
                # 中等角度偏差，使用平滑弧线路径
                return self._generate_arc_path(start_state, goal_state)

        except Exception:
            # 异常情况返回空路径
            return None

    def _generate_forward_path(self, start_state: State, goal_state: State) -> Path:
        """生成前进路径（小角度调整）"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        path = [start_state]

        # 添加3-5个中间点，逐步调整朝向
        num_points = 4
        for i in range(1, num_points):
            ratio = i / num_points

            # 位置插值
            x = x1 + ratio * (x2 - x1)
            y = y1 + ratio * (y2 - y1)

            # 朝向平滑插值
            theta = theta1 + ratio * (theta2 - theta1)
            theta = np.arctan2(np.sin(theta), np.cos(theta))

            path.append((x, y, theta))

        path.append(goal_state)
        return path

    def _generate_arc_path(self, start_state: State, goal_state: State) -> Path:
        """生成弧线路径（中等角度）"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 计算转弯中心
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 使用较大的转弯半径确保平滑
        turn_radius = max(distance / 2, self.wheelbase * 3)

        path = [start_state]

        # 生成弧线上的点
        num_points = max(6, int(distance / 2))  # 根据距离调整点数

        for i in range(1, num_points):
            ratio = i / num_points

            # 使用三次贝塞尔曲线生成平滑路径
            # 控制点设计确保符合车辆运动学
            p0 = np.array([x1, y1])
            p3 = np.array([x2, y2])

            # 控制点基于起始和结束朝向
            control_distance = distance / 3
            p1 = p0 + control_distance * np.array([np.cos(theta1), np.sin(theta1)])
            p2 = p3 - control_distance * np.array([np.cos(theta2), np.sin(theta2)])

            # 贝塞尔曲线计算
            t = ratio
            pos = (1 - t) ** 3 * p0 + 3 * (1 - t) ** 2 * t * p1 + 3 * (1 - t) * t ** 2 * p2 + t ** 3 * p3

            # 计算该点的朝向（切线方向）
            if i < num_points - 1:
                # 计算切线方向
                derivative = 3 * (1 - t) ** 2 * (p1 - p0) + 6 * (1 - t) * t * (p2 - p1) + 3 * t ** 2 * (p3 - p2)
                theta = np.arctan2(derivative[1], derivative[0])
            else:
                theta = theta2

            path.append((pos[0], pos[1], theta))

        path.append(goal_state)
        return path

    def _generate_complex_path(self, start_state: State, goal_state: State) -> Path:
        """生成复杂机动路径（大角度或倒车）"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        path = [start_state]

        # 策略：先转向，再移动，最后调整
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 第一阶段：原地转向调整
        target_direction = np.arctan2(y2 - y1, x2 - x1)
        intermediate_theta = target_direction

        # 添加转向过程的中间点
        for i in range(1, 4):
            ratio = i / 4
            theta = theta1 + ratio * (intermediate_theta - theta1)
            theta = np.arctan2(np.sin(theta), np.cos(theta))
            path.append((x1, y1, theta))

        # 第二阶段：沿目标方向移动
        num_move_points = max(4, int(distance / 3))
        for i in range(1, num_move_points):
            ratio = i / num_move_points
            x = x1 + ratio * (x2 - x1)
            y = y1 + ratio * (y2 - y1)
            path.append((x, y, intermediate_theta))

        # 第三阶段：最终朝向调整
        for i in range(1, 4):
            ratio = i / 4
            theta = intermediate_theta + ratio * (theta2 - intermediate_theta)
            theta = np.arctan2(np.sin(theta), np.cos(theta))
            path.append((x2, y2, theta))

        path.append(goal_state)
        return path

    def _apply_motion_with_kinematics(self, state: Tuple[float, float, float],
                                      motion: Tuple[float, float]) -> Tuple[float, float, float]:
        """使用车辆运动学模型应用运动（Hybrid A*核心）"""
        x, y, theta = state
        distance, steering_angle = motion

        # 使用自行车模型（bicycle model）- Hybrid A*的核心
        if abs(steering_angle) < 0.01:  # 直行
            new_x = x + distance * np.cos(theta)
            new_y = y + distance * np.sin(theta)
            new_theta = theta
        else:
            # 计算转弯半径（考虑轴距）
            turning_radius = self.wheelbase / np.tan(abs(steering_angle))

            # 计算角度变化
            delta_theta = distance / turning_radius
            if steering_angle < 0:  # 右转
                delta_theta = -delta_theta

            # 计算新位置（使用圆弧运动）
            if distance > 0:  # 前进
                new_x = x + turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y - turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))
            else:  # 后退
                new_x = x - turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y + turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))

            new_theta = theta + delta_theta

        # 规范化角度到[-π, π]
        new_theta = np.arctan2(np.sin(new_theta), np.cos(new_theta))

        return (new_x, new_y, new_theta)

    def _apply_motion(self, state: Tuple[float, float, float],
                      motion: Tuple[float, float]) -> Tuple[float, float, float]:
        """应用运动原语 - 使用车辆运动学模型"""
        x, y, theta = state
        distance, steering_angle = motion

        # 限制转向角
        steering_angle = np.clip(steering_angle, -self.max_steering_angle, self.max_steering_angle)

        # 使用自行车模型（bicycle model）
        if abs(steering_angle) < 0.01:  # 直行
            new_x = x + distance * np.cos(theta)
            new_y = y + distance * np.sin(theta)
            new_theta = theta
        else:
            # 计算转弯半径
            turning_radius = self.wheelbase / np.tan(abs(steering_angle))

            # 计算角度变化
            delta_theta = distance / turning_radius
            if steering_angle < 0:  # 右转
                delta_theta = -delta_theta

            # 计算新位置
            if distance > 0:  # 前进
                new_x = x + turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y - turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))
            else:  # 后退
                new_x = x - turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y + turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))

            new_theta = theta + delta_theta

        # 规范化角度到[-π, π]
        new_theta = np.arctan2(np.sin(new_theta), np.cos(new_theta))

        return (new_x, new_y, new_theta)

    def _is_collision_free(self, state: Tuple[float, float, float],
                           obstacles: List[dict]) -> bool:
        """简单的碰撞检测"""
        x, y, _ = state

        for obs in obstacles:
            distance = np.linalg.norm(np.array([x, y]) - obs['position'])
            if distance < 3.5:  # 简单的安全距离
                return False

        return True

    def _heuristic(self, state: Tuple[float, float, float],
                   goal: Tuple[float, float, float]) -> float:
        """简化的启发式函数 - 主要考虑位置距离"""
        x1, y1, theta1 = state
        x2, y2, theta2 = goal

        # 位置距离（主要因素）
        position_distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 简化的朝向代价
        final_angle_diff = abs(theta2 - theta1)
        final_angle_diff = min(final_angle_diff, 2 * np.pi - final_angle_diff)
        angle_cost = 0.5 * final_angle_diff  # 降低朝向权重

        # 简化的组合代价
        return position_distance + angle_cost

    def _is_goal_reached(self, state: Tuple[float, float, float],
                         goal: Tuple[float, float, float]) -> bool:
        """检查是否到达目标 - 放宽条件提高搜索成功率"""
        x1, y1, theta1 = state
        x2, y2, theta2 = goal

        # 位置容差 - 进一步放宽
        position_distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        position_tolerance = 4.0  # 4.0米位置容差

        # 朝向容差 - 进一步放宽
        angle_diff = abs(theta2 - theta1)
        angle_diff = min(angle_diff, 2 * np.pi - angle_diff)
        angle_tolerance = np.pi / 2  # 90度朝向容差

        # 同时满足位置和朝向要求
        return position_distance < position_tolerance and angle_diff < angle_tolerance

    def _state_to_node(self, state: Tuple[float, float, float]) -> Tuple[int, int, int]:
        """将状态转换为节点（用于哈希）- 降低精度提高搜索效率"""
        x, y, theta = state

        # 位置离散化：1米精度（降低精度）
        x_discrete = round(x)
        y_discrete = round(y)

        # 朝向离散化：30度精度（12个方向，降低精度）
        theta_normalized = np.arctan2(np.sin(theta), np.cos(theta))  # 规范化到[-π, π]
        theta_discrete = round(theta_normalized / (np.pi / 6)) * (np.pi / 6)

        return (int(x_discrete), int(y_discrete), int(theta_discrete / (np.pi / 6)))

    def _motion_cost(self, motion: Tuple[float, float]) -> float:
        """改进的运动代价 - 考虑距离、转向和方向"""
        distance, steering_angle = motion

        # 基础距离代价
        distance_cost = abs(distance)

        # 转向代价：转向角度越大代价越高
        steering_cost = 0.8 * abs(steering_angle)

        # 后退代价：后退比前进代价更高
        reverse_penalty = 0.3 if distance < 0 else 0

        # 急转弯代价：大角度转向额外惩罚
        sharp_turn_penalty = 0.5 if abs(steering_angle) > 0.4 else 0

        return distance_cost + steering_cost + reverse_penalty + sharp_turn_penalty

    def _reconstruct_path(self, came_from: dict, current: Tuple,
                          start: Tuple) -> List[Tuple[float, float, float]]:
        """重构路径"""
        path = []
        while current in came_from:
            parent_node, state = came_from[current]
            path.append(state)
            current = parent_node
            if current == start:
                break
        path.reverse()
        return path

    def _generate_intermediate_path(self, start_state: Tuple[float, float, float],
                                    goal_state: Tuple[float, float, float]) -> List[Tuple[float, float, float]]:
        """生成带中间点的路径"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 计算距离和方向
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 如果距离很近，直接连接
        if distance < 5.0:
            return [start_state, goal_state]

        # 生成中间点
        path = [start_state]

        # 添加1-2个中间点
        num_intermediate = min(2, int(distance / 8.0))

        for i in range(1, num_intermediate + 1):
            ratio = i / (num_intermediate + 1)

            # 线性插值位置
            x_mid = x1 + ratio * (x2 - x1)
            y_mid = y1 + ratio * (y2 - y1)

            # 插值朝向
            theta_mid = theta1 + ratio * (theta2 - theta1)

            # 规范化角度
            theta_mid = np.arctan2(np.sin(theta_mid), np.cos(theta_mid))

            path.append((x_mid, y_mid, theta_mid))

        path.append(goal_state)
        return path


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv
    集成基础版Hybrid A*路径规划和平滑度控制
    支持随机初始位置生成
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 初始化路径规划器和相关变量
        self.path_planner = SimpleHybridAStar()
        self.reference_path = None
        self.path_index = 0
        self.last_action = None
        self.last_raw_action = None  # 用于动作平滑

        # 随机生成配置
        self.enable_random_spawn = getattr(config, 'enable_random_spawn', True)  # 是否启用随机生成
        self.spawn_margin = getattr(config, 'spawn_margin', 8.0)  # 与边界的安全距离

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 限制动作空间，避免极端动作
        self.action_space = gym.spaces.Box(
            low=np.array([-1.5, -0.3]),  # [加速度, 转向]
            high=np.array([1.5, 0.3]),
            dtype=np.float32
        )

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """重置环境并规划路径"""
        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        obs, info = super().reset(seed=seed, options=options)

        # 规划路径
        self._plan_path()

        # 重置平滑度控制变量
        self.last_action = None
        self.last_raw_action = None
        self.path_index = 0

        obs = self._flatten_observation(obs)

        # 添加路径信息到info中
        if self.reference_path:
            info.update({
                'path_length': len(self.reference_path),
                'path_planned': True
            })
        else:
            info.update({
                'path_length': 0,
                'path_planned': False
            })

        return obs, info

    def _create_vehicles(self) -> None:
        """
        重写车辆创建方法，支持随机初始位置生成

        包括：
        1. 创建可控制的车辆（玩家车辆）- 支持随机x位置
        2. 设置目标停车位
        3. 添加其他静止车辆
        4. 添加边界墙壁
        """
        # 获取所有可用的停车位
        empty_spots = list(self.road.network.lanes_dict().keys())

        # 1. 创建可控制车辆（玩家车辆）- 支持随机位置
        self.controlled_vehicles = []
        for i in range(self.config["controlled_vehicles"]):
            # 生成随机初始位置
            if self.enable_random_spawn:
                x0 = self._generate_random_spawn_x()
            else:
                # 使用原始的固定位置逻辑
                x0 = (i - self.config["controlled_vehicles"] // 2) * 10

            # 创建车辆，随机朝向
            vehicle = self.action_type.vehicle_class(
                self.road,                                    # 道路对象
                [x0, 0],                                     # 初始位置（y=0固定，x随机）
                2 * np.pi * self.np_random.uniform(),        # 随机朝向
                0                                            # 初始速度
            )
            # 设置车辆颜色为EGO颜色
            try:
                from HighwayEnv.highway_env.vehicle.graphics import VehicleGraphics
                vehicle.color = VehicleGraphics.EGO_COLOR
            except:
                vehicle.color = [255, 100, 100]  # 备用颜色
            self.road.vehicles.append(vehicle)               # 添加到道路车辆列表
            self.controlled_vehicles.append(vehicle)         # 添加到控制车辆列表

            # 从空位中移除车辆当前占据的位置
            if hasattr(vehicle, 'lane_index') and vehicle.lane_index in empty_spots:
                empty_spots.remove(vehicle.lane_index)

        # 2. 设置目标位置
        for vehicle in self.controlled_vehicles:
            if empty_spots:  # 确保还有空位可选
                # 随机选择一个空车位作为目标
                lane_index = empty_spots[self.np_random.choice(np.arange(len(empty_spots)))]
                lane = self.road.network.get_lane(lane_index)
                # 创建目标标记
                from HighwayEnv.highway_env.vehicle.objects import Landmark
                vehicle.goal = Landmark(
                    self.road,                     # 道路对象
                    lane.position(lane.length / 2, 0),  # 目标位置（车道中点）
                    heading=lane.heading               # 目标朝向
                )
                self.road.objects.append(vehicle.goal)  # 添加目标到道路对象列表
                empty_spots.remove(lane_index)          # 从空位中移除

        # 3. 创建其他车辆
        for i in range(self.config["vehicles_count"]):
            if not empty_spots:  # 如果没有空位了就跳出
                continue
            # 随机选择一个空位
            lane_index = empty_spots[self.np_random.choice(np.arange(len(empty_spots)))]
            # 创建静止的车辆
            from HighwayEnv.highway_env.vehicle.kinematics import Vehicle
            v = Vehicle.make_on_lane(self.road, lane_index, 4, speed=0)
            self.road.vehicles.append(v)  # 添加到道路车辆列表
            empty_spots.remove(lane_index)  # 从空位中移除

        # 4. 添加墙壁
        if self.config["add_walls"]:
            width, height = 70, 42  # 设置墙壁的宽度和高度

            # 添加上下墙
            for y in [-height / 2, height / 2]:  # 上下墙的y坐标
                from HighwayEnv.highway_env.vehicle.objects import Obstacle
                obstacle = Obstacle(self.road, [0, y])  # 创建障碍物
                obstacle.LENGTH, obstacle.WIDTH = (width, 1)  # 设置尺寸
                # 计算对角线长度（用于碰撞检测）
                obstacle.diagonal = np.sqrt(obstacle.LENGTH**2 + obstacle.WIDTH**2)
                self.road.objects.append(obstacle)  # 添加到道路对象列表

            # 添加左右墙
            for x in [-width / 2, width / 2]:  # 左右墙的x坐标
                from HighwayEnv.highway_env.vehicle.objects import Obstacle
                obstacle = Obstacle(self.road, [x, 0], heading=np.pi / 2)  # 创建垂直障碍物
                obstacle.LENGTH, obstacle.WIDTH = (height, 1)  # 设置尺寸
                # 计算对角线长度（用于碰撞检测）
                obstacle.diagonal = np.sqrt(obstacle.LENGTH**2 + obstacle.WIDTH**2)
                self.road.objects.append(obstacle)  # 添加到道路对象列表

    def _generate_random_spawn_x(self) -> float:
        """
        生成随机的x坐标初始位置，确保与边界保持安全距离

        Returns:
            float: 随机的x坐标
        """
        # 停车场边界设置（基于父类的墙壁配置）
        parking_width = 70  # 停车场总宽度
        wall_boundary = parking_width / 2  # 左右墙壁位置：±35

        # 计算安全的x坐标范围
        # 考虑车辆长度（5米）和额外安全距离
        vehicle_half_length = 2.5  # 车辆半长
        safe_margin = self.spawn_margin  # 配置的安全距离

        # 安全范围：从 -(35-8-2.5) 到 +(35-8-2.5)
        min_x = -wall_boundary + safe_margin + vehicle_half_length
        max_x = wall_boundary - safe_margin - vehicle_half_length

        # 确保范围有效
        if min_x >= max_x:
            # 如果安全距离设置过大，回退到中央位置
            print(f"警告：安全距离设置过大，使用中央位置。min_x={min_x}, max_x={max_x}")
            return 0.0

        # 生成随机x坐标
        random_x = self.np_random.uniform(min_x, max_x)

        return random_x

    def _plan_path(self):
        """使用Hybrid A*规划路径"""
        try:
            vehicle = self.controlled_vehicles[0]
            start_state = (vehicle.position[0], vehicle.position[1], vehicle.heading)
            goal_state = (vehicle.goal.position[0], vehicle.goal.position[1], vehicle.goal.heading)

            # 获取障碍物
            obstacles = self._get_obstacles()

            # 使用Hybrid A*规划
            self.reference_path = self.path_planner.plan(start_state, goal_state, obstacles)

            if self.reference_path is None or len(self.reference_path) < 2:
                # 如果规划失败，使用简单的直线路径
                self.reference_path = [start_state, goal_state]

        except Exception:
            # 出错时使用简单路径
            vehicle = self.controlled_vehicles[0]
            start_state = (vehicle.position[0], vehicle.position[1], vehicle.heading)
            goal_state = (vehicle.goal.position[0], vehicle.goal.position[1], vehicle.goal.heading)
            self.reference_path = [start_state, goal_state]

    def _get_obstacles(self):
        """获取障碍物信息"""
        obstacles = []

        try:
            # 其他车辆作为障碍物
            for vehicle in self.road.vehicles:
                if vehicle != self.controlled_vehicles[0]:
                    obstacles.append({
                        'position': vehicle.position,
                        'type': 'vehicle'
                    })
        except:
            pass  # 如果获取障碍物失败，返回空列表

        return obstacles

    def _get_distance_to_path(self):
        """获取车辆到规划路径的最小距离和最近路径点信息"""
        # 检查是否存在有效的规划路径
        if not self.reference_path:
            return float('inf'), -1, None  # 无路径时返回无穷大距离

        try:
            # 获取当前被控制的车辆对象
            vehicle = self.controlled_vehicles[0]

            # 初始化搜索变量
            min_distance = float('inf')    # 最小距离，初始为无穷大
            closest_index = 0              # 最近路径点的索引
            closest_waypoint = None        # 最近的路径点坐标

            # 遍历规划路径中的每个路径点
            for i, waypoint in enumerate(self.reference_path):
                # 计算车辆当前位置到路径点的欧几里得距离
                # waypoint[:2] 提取路径点的x,y坐标（忽略朝向角度）
                # vehicle.position 是车辆当前的[x, y]坐标
                distance = np.linalg.norm(vehicle.position - np.array(waypoint[:2]))

                # 如果当前距离小于已记录的最小距离，更新最近点信息
                if distance < min_distance:
                    min_distance = distance      # 更新最小距离
                    closest_index = i           # 更新最近点索引
                    closest_waypoint = waypoint # 更新最近点坐标

            # 返回三个值：最小距离、最近点索引、最近点坐标
            return min_distance, closest_index, closest_waypoint

        except:
            # 异常情况下返回安全默认值
            return float('inf'), -1, None

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        # 简单的动作平滑
        original_action = np.array(action)
        if hasattr(self, 'last_raw_action') and self.last_raw_action is not None:
            action = 0.7 * np.array(action) + 0.3 * np.array(self.last_raw_action)
        self.last_raw_action = original_action.copy()

        obs, reward, terminated, truncated, info = super().step(action)
        obs = self._flatten_observation(obs)

        # 添加详细的调试信息到info中
        try:
            # 计算各项奖励分量
            base_reward_value = super()._reward(action)
            path_reward_value = self._compute_path_following_reward()
            smoothness_reward_value = self._compute_smoothness_reward(action)

            if self.reference_path:
                vehicle = self.controlled_vehicles[0]
                min_dist = min(np.linalg.norm(vehicle.position - np.array(wp[:2]))
                               for wp in self.reference_path)

                info.update({
                    'path_distance': min_dist,
                    'path_index': self.path_index,
                    'path_following_reward': path_reward_value
                })

            # 添加平滑度和动作信息
            if self.last_action is not None:
                action_change = np.linalg.norm(np.array(action) - np.array(self.last_action))
                info.update({
                    'action_change': action_change,
                    'smoothness_reward': smoothness_reward_value,
                    'is_smooth_action': action_change < 0.3
                })

            # 添加奖励分解信息
            info.update({
                'reward_breakdown': {
                    'base_reward': base_reward_value,
                    'path_reward': path_reward_value,
                    'smoothness_reward': smoothness_reward_value,
                    'total_reward': reward
                },
                'original_action': original_action,
                'smoothed_action': action,
                'action_smoothing_applied': np.linalg.norm(original_action - action) > 0.001
            })
        except:
            pass  # 如果添加信息失败，不影响主要功能

        # 标记需要重新绘制路径
        self._need_redraw_path = True

        return obs, reward, terminated, truncated, info

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def _reward(self, action: np.ndarray) -> float:
        """计算综合奖励函数"""
        # 获取环境基础奖励（距离、成功、碰撞等）
        base_reward = super()._reward(action)

        # 计算路径跟踪奖励（鼓励沿规划路径行驶）
        path_reward = self._compute_path_following_reward()

        # 计算动作平滑度奖励（鼓励平滑驾驶）
        smoothness_reward = self._compute_smoothness_reward(action)

        # 加权组合三种奖励（平衡路径跟踪和运动鼓励）
        total_reward = (
                0.3 * base_reward +      # 基础任务完成奖励（提高，鼓励完成任务）
                0.4 * path_reward +      # 路径跟踪奖励（包含前进激励）
                0.3 * smoothness_reward  # 平滑驾驶奖励
        )

        return total_reward

    def _compute_path_following_reward(self):
        """计算路径跟踪奖励：基于车辆到规划路径的距离"""
        # 检查是否有有效的规划路径
        if not self.reference_path or len(self.reference_path) < 2:
            return 0.0

        try:
            # 获取当前控制车辆
            vehicle = self.controlled_vehicles[0]

            # 计算车辆到路径的最短距离和最近路径点
            min_distance, closest_index, closest_waypoint = self._get_distance_to_path()

            # 检查是否找到有效的最近点
            if closest_index == -1:
                return 0.0

            # 更新当前路径索引（用于跟踪进度）
            self.path_index = closest_index

            # 基于距离的奖励
            # 基于距离计算奖励（放宽约束，鼓励前进）
            distance_reward = 0
            if min_distance < 4.0:
                # 在路径附近：给予奖励，鼓励保持
                distance_reward = 0.2 - min_distance * 0.03  # 0米=0.2分，4米=0.08分
            elif min_distance < 8.0:
                # 中等偏离：轻微惩罚，但不阻止前进
                distance_reward = -0.02 - (min_distance - 4.0) * 0.01  # 4米=-0.02分，8米=-0.06分
            else:
                # 严重偏离：适度惩罚
                distance_reward = -0.1  # 超过8米惩罚

            # 添加前进激励奖励
            progress_reward = self._compute_progress_reward()

            return distance_reward + progress_reward

        except:
            # 异常情况返回零奖励
            return 0.0

    def _compute_progress_reward(self):
        """计算前进进度奖励：鼓励车辆沿路径前进"""
        if not hasattr(self, 'last_path_index'):
            self.last_path_index = 0
            return 0.0

        try:
            # 获取当前路径索引
            _, current_index, _ = self._get_distance_to_path()

            if current_index == -1:
                return 0.0

            # 计算路径进度变化
            progress_change = current_index - self.last_path_index

            # 更新记录
            self.last_path_index = current_index

            # 前进奖励
            if progress_change > 0:
                return 0.05 * progress_change  # 每前进一个路径点奖励0.05
            elif progress_change < 0:
                return -0.02 * abs(progress_change)  # 后退轻微惩罚
            else:
                return 0.0  # 停留在同一点无奖励无惩罚

        except:
            return 0.0

    def _compute_smoothness_reward(self, action):
        """细化的平滑度奖励：分别优化速度控制和转向控制"""
        # 首次调用时初始化上一次动作
        if self.last_action is None:
            self.last_action = action.copy() if hasattr(action, 'copy') else np.array(action)
            return 0.0

        try:
            # 分别计算速度和转向的平滑度奖励
            speed_reward = self._compute_speed_control_reward(action)
            steering_reward = self._compute_steering_control_reward(action)

            # 保存当前动作供下次比较
            self.last_action = action.copy() if hasattr(action, 'copy') else np.array(action)

            # 组合平滑度奖励（速度控制更重要）
            return 0.6 * speed_reward + 0.4 * steering_reward

        except:
            # 异常情况返回零奖励
            return 0.0

    def _compute_speed_control_reward(self, action):
        """速度控制奖励：鼓励合适的速度和平滑加速"""
        acceleration = action[0]
        last_acceleration = self.last_action[0]

        speed_penalty = 0.0

        # 1. 鼓励适中的速度（重点鼓励前进）
        if abs(acceleration) > 0.8:  # 速度过快
            speed_penalty += (abs(acceleration) - 0.8) * 0.4  # 惩罚
        elif abs(acceleration) > 0.6:  # 速度较快
            speed_penalty += (abs(acceleration) - 0.6) * 0.2  # 惩罚
        elif abs(acceleration) < 0.02:  # 几乎不动
            speed_penalty += 0.08  # 增加对不动的惩罚
        elif 0.1 < abs(acceleration) < 0.5:  # 适中速度
            speed_penalty -= 0.02  # 奖励适中的前进速度

        # 2. 平滑加速奖励（防止急加速/急减速）
        accel_change = abs(acceleration - last_acceleration)
        if accel_change > 0.4:  # 加速度变化过大
            speed_penalty += accel_change * 0.5
        elif accel_change < 0.1:  # 加速度变化平滑
            speed_penalty -= 0.02  # 小奖励

        # 3. 渐进式速度调整奖励
        if 0.1 < abs(acceleration) < 0.6:  # 适中的速度
            speed_penalty -= 0.03  # 奖励适中速度

        return -speed_penalty

    def _compute_steering_control_reward(self, action):
        """转向控制奖励：鼓励渐进式转向和有效转向"""
        if len(action) < 2:
            return 0.0

        steering = action[1]
        last_steering = self.last_action[1] if len(self.last_action) > 1 else 0

        steering_penalty = 0.0
        steering_bonus = 0.0

        # 1. 转向变化平滑度（防止急转向）
        steering_change = abs(steering - last_steering)
        if steering_change > 0.3:  # 转向变化过大
            steering_penalty += steering_change * 0.4
        elif steering_change < 0.05:  # 转向变化平滑
            steering_bonus += 0.01

        # 2. 渐进转向奖励（鼓励逐步调整）
        steer_magnitude = abs(steering)
        if 0.05 < steer_magnitude < 0.4:  # 适中的转向
            steering_bonus += 0.02
        elif steer_magnitude > 0.7:  # 过度转向
            steering_penalty += (steer_magnitude - 0.7) * 0.5

        # 3. 转向有效性奖励
        if self.reference_path:
            effectiveness = self._evaluate_steering_effectiveness(action)
            if effectiveness > 0.7:  # 高效转向
                steering_bonus += 0.03
            elif effectiveness < 0.3:  # 低效转向
                steering_penalty += 0.02

        # 4. 直线行驶时的转向惩罚
        if self._is_straight_path_ahead() and steer_magnitude > 0.2:
            steering_penalty += steer_magnitude * 0.3  # 直线时不应大幅转向

        return -steering_penalty + steering_bonus

    def _is_straight_path_ahead(self):
        """判断前方路径是否为直线（用于转向控制）"""
        if not self.reference_path or len(self.reference_path) < 3:
            return True  # 路径太短，默认为直线

        try:
            # 获取当前位置在路径中的索引
            _, current_index, _ = self._get_distance_to_path()

            # 检查前方3个路径点的角度变化
            if current_index >= 0 and current_index + 2 < len(self.reference_path):
                # 计算连续路径段的角度变化
                p1 = self.reference_path[current_index]
                p2 = self.reference_path[current_index + 1]
                p3 = self.reference_path[current_index + 2]

                # 计算两个路径段的方向角
                angle1 = np.arctan2(p2[1] - p1[1], p2[0] - p1[0])
                angle2 = np.arctan2(p3[1] - p2[1], p3[0] - p2[0])

                # 计算角度变化
                angle_change = abs(angle2 - angle1)
                angle_change = min(angle_change, 2*np.pi - angle_change)

                # 角度变化小于15度认为是直线
                return angle_change < np.pi/12

            return True

        except:
            return True

    def _evaluate_steering_effectiveness(self, action):
        """评估转向动作的有效性：判断转向是否朝向正确方向"""
        # 检查输入有效性
        if not self.reference_path or len(action) < 2:
            return 0.0

        try:
            # 获取当前车辆状态
            vehicle = self.controlled_vehicles[0]
            steering_angle = action[1]  # 转向角度（正值=左转，负值=右转）

            # 获取最近的路径点
            min_distance, _, closest_waypoint = self._get_distance_to_path()

            # 检查是否找到有效路径点
            if closest_waypoint is None:
                return 0.0

            # 计算从当前位置到目标路径点的方向
            target_direction = np.arctan2(
                closest_waypoint[1] - vehicle.position[1],  # y方向差
                closest_waypoint[0] - vehicle.position[0]   # x方向差
            )

            # 计算车头朝向与目标方向的角度差
            heading_diff = target_direction - vehicle.heading
            heading_diff = np.arctan2(np.sin(heading_diff), np.cos(heading_diff))  # 规范化到[-π,π]

            # 评估转向有效性
            if abs(heading_diff) < 0.1:  # 车头已经基本朝向目标（约6度内）
                # 不需要转向时，小转向=好，大转向=差
                return 1.0 if abs(steering_angle) < 0.1 else 0.2
            elif heading_diff > 0:  # 目标在左侧，需要左转
                # 左转有效性：正转向角度越大越好
                return max(0, steering_angle) / 0.6
            else:  # 目标在右侧，需要右转
                # 右转有效性：负转向角度绝对值越大越好
                return max(0, -steering_angle) / 0.6

        except:
            # 异常情况返回无效
            return 0.0

    def _draw_planned_path_on_surface(self, surface):
        """在指定surface上绘制规划路径"""
        try:
            # 检查基本条件
            if not self.reference_path or len(self.reference_path) < 2:
                return

            # 导入pygame
            import pygame

            # 转换路径点为屏幕坐标
            screen_points = []
            for waypoint in self.reference_path:
                x, y = waypoint[:2]
                screen_x, screen_y = surface.pos2pix(x, y)
                screen_points.append((int(screen_x), int(screen_y)))

            if len(screen_points) < 2:
                return

            # 绘制路径线（红色粗线 - 表示Hybrid A*规划路径）
            pygame.draw.lines(surface, (255, 50, 50), False, screen_points, 6)

            # 绘制起点（绿色圆圈）
            pygame.draw.circle(surface, (50, 255, 50), screen_points[0], 12)
            pygame.draw.circle(surface, (255, 255, 255), screen_points[0], 12, 2)

            # 绘制终点（蓝色方块 - 目标停车位）
            target_size = 10
            target_rect = pygame.Rect(
                screen_points[-1][0] - target_size,
                screen_points[-1][1] - target_size,
                target_size * 2,
                target_size * 2
            )
            pygame.draw.rect(surface, (50, 150, 255), target_rect)
            pygame.draw.rect(surface, (255, 255, 255), target_rect, 2)

            # 绘制路径点（小红色圆点）
            for i in range(1, len(screen_points) - 1):
                pygame.draw.circle(surface, (200, 100, 100), screen_points[i], 4)

        except Exception:
            pass  # 静默处理绘制错误

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self, mode='human'):
        """
        重写渲染方法，设置固定摄像头视角并绘制路径
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

            # 使用agent_display机制来绘制路径
            def path_display(agent_surface, sim_surface):
                """使用agent_display机制绘制路径"""
                # agent_surface暂时不用，只在sim_surface上绘制
                _ = agent_surface  # 标记为故意未使用
                self._draw_planned_path_on_surface(sim_surface)

            # 设置agent_display
            self.viewer.set_agent_display(path_display)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result


def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
